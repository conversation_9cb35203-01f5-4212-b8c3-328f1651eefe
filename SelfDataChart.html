<!DOCTYPE html>
<html style="height: 100%">
   <head>
       <meta charset="utf-8">
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
   </head>
   <body style="height: 100%; margin: 0">
       <div id="container" style="height: 100%"></div>
       <script type="text/javascript" src="echarts.common.min.js"></script>

       <script type="text/javascript">
	   
function strToDate(str)
{
  var arys= new Array();
  arys=str.split('-');
  var newDate=new Date(arys[0],arys[1],arys[2]); 
  return newDate;
  
}

function MaxIndex(arr)
{
	var max = -50000;
	var index = -1;
	for(var i=0;i<arr.length;i++){ 
	  if(max<arr[i]){
		max=arr[i];
		index = i;
	  }
	}
	return index;
}

function MinIndex(arr)
{
	var max = 500000000;
	var index = -1;
	for(var i=0;i<arr.length;i++){ 
	  if(max>arr[i]){
		max=arr[i];
		index = i;
	  }
	}
	return index;
}

var dom = document.getElementById("container");
var myChart = echarts.init(dom);
var g_percent = external.GetPercent();

function RefreshChart()
{

var count = external.GetCapitalCount();
var title = external.GetTitle();
var charPeriod = external.GetPeriod();
var switchName2 = "全部";

var title2 = external.GetTitleN(2);
var count2 = external.GetDataCount(2);
var name = '数据';
if (title2 == '持仓数量')
	name = '持仓市值';
else if (title2 == '持仓市值')
	name = '持仓数量';
if (charPeriod == 0)
{

	switchName2 = "最近        一个月";
}
else if (charPeriod == 1)
{
	switchName2 = "最近        两个月";
	title+="-近一月";
}
else if (charPeriod == 2)
{
	switchName2 = "最近        三个月";
	title+="-近两月";
}
else if (charPeriod == 3)
{
	switchName2 = "最近        半年";
	title+="-近三月";
}
else if (charPeriod == 4)
{
	switchName2 = "最近        一年";
	title+="-近半年";
}
else if (charPeriod == 5)
{
	switchName2 = "今年";
	charName+="-近一年";
}
else
	charName+="-今年";
	
var indexBase = 1;
for (var i = 0; i < count; i++) {
    //var now = new Date(external.GetCapitalDate(i));
	//var now = strToDate(external.GetCapitalDateS(i));
	var now = external.GetCapitalDateS(i);
    date.push(now);
    data.push(external.GetCapital(i));
	data4.push(external.GetAvaliable(i));
	
	if (!g_percent && count2 > 0)
	{
		data2.push(external.GetData(2,i))
		continue;
	}
	
	if (g_percent)
	{
		count2 = 0;
		if (i == 0){
			
			data2.push(0);
			data3.push(0);
			data5.push(0);
			data6.push(0);
			indexBase = data[0];//external.GetCapitalIndex(i) ;
		}
		else{
			var change = ((data[i] -  data[0]) / data[0])*100;
			data2.push(change);
			
			change = ((external.GetCapitalIndex(i) -  indexBase) / indexBase)*100;
			data3.push(change);
			var day_return = data4[i] - data4[i-1];
			if (day_return < 0)
				day_return = 0;
			data5.push(day_return);
			data6.push(data6[i-1] + day_return);
		}
	}
	
}

var data2minIndex = MinIndex(data2);
var data2maxIndex = MaxIndex(data2);

var data3minIndex = MinIndex(data3);
var data3maxIndex = MaxIndex(data3);

var data2min = data2[data2minIndex];
var data2max = data2[data2maxIndex];

var data3min = data3[data3minIndex];
var data3max = data3[data3maxIndex];

var datamin = Math.min(data2min, data3min);
var datamax = Math.max(data2max, data3max);
var yaxismin = data[0] * (1 + datamin/100);
var yaxismax = data[0] * (1 + datamax/100);


var data4minIndex = MinIndex(data6);
var data4maxIndex = MaxIndex(data6);


var data4min = data6[data4minIndex];
var data4max = data6[data4maxIndex];


option = {
    tooltip: {
        trigger: 'axis',
		    formatter:function(params)  
        {  
           var relVal = params[0].name;  
           for (var i = 0, l = params.length; i < l; i++) {  
				if (i == 0)
					relVal += '<br/>' + params[i].seriesName + ' : ' + params[i].value.toFixed(2);  
				else
					relVal += '<br/>' + params[i].seriesName + ' : ' + params[i].value.toFixed(2);  
            }  
           return relVal;  
        },
        confine: true,  // 限制在图表区域内
		position: function(point, params, dom, rect, size) 
		{
			var posX = point[0];
			var posY = point[1];
			var viewWidth = size.viewSize[0];
			var viewHeight = size.viewSize[1];
			var boxWidth = size.contentSize[0];
			var boxHeight = size.contentSize[1];

			// 获取当前点的索引
			var dataIndex = params[0].dataIndex;
			var totalDataCount = params[0].data.length;
			
			// 如果是最后两个点，将tooltip显示在左侧
			if (dataIndex >= totalDataCount - 2) {
				
				posX = posX - boxWidth;
			} else {
				// 调整tooltip位置，确保其不会超出右边界
				if (posX + boxWidth > viewWidth) {
					posX = posX - boxWidth;
				}
			}

			// 调整tooltip位置，确保其不会超出上/下边界
			if (posY + boxHeight > viewHeight) {
				posY = viewHeight - boxHeight;
			} else if (posY < 0) {
				posY = 0;
			}

			return [posX, posY];
        }
    },
    title: {
        left: 'center',
        text: title
    },

	legend: {
        data: [name, count2 > 0 ? title2 : null],
        right: '4%',
		 show: true 

    },
	grid: {
		left: '70',
		right: '70'
	},
	toolbox: {
		x:'left',
		left:"4%",
        feature: {

			    myTool1: {
                show: true,
                title: '导出数据',
                icon: 'path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891',
                onclick: function (){
                    external.Command(0);
                }
				},
				myTool13: {
                show: true,
                title: switchName2 ,
                icon: 'path://M744.838095 279.161905c-128-128-336.457143-128-465.67619 0-128 128-128 336.457143 0 465.67619 128 128 336.457143 128 465.67619 0 128-128 128-336.457143 0-465.67619z m-30.47619 304.761905H463.238095V344.990476h60.952381v177.980953h190.171429v60.952381z',
                onclick: function (){
					external.Command(3);

                }
				},
				myTool12: {
                show: true,
                title: '显示             %坐标',
                icon: 'path://M433.066667 402.773333a20.48 20.48 0 0 1 29.866666 0l113.066667 113.066667 296.106667-296.106667a21.333333 21.333333 0 0 1 30.293333 0l29.866667 29.866667a21.333333 21.333333 0 0 1 0 30.293333l-341.333334 341.333334a20.48 20.48 0 0 1-29.866666 0l-113.066667-113.066667-296.106667 296.106667a21.333333 21.333333 0 0 1-30.293333 0l-29.866667-29.866667a21.333333 21.333333 0 0 1 0-30.293333z',
                onclick: function (){
					
					date.length = 0;
					data.length = 0;
					data2.length = 0;
					data3.length = 0;
					data4.length = 0;
					data5.length = 0;
					data6.length = 0;
					count2 = 0;
					external.Command(100);
					g_percent = !g_percent;
					RefreshChart();
                }
				},
				
        }
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: date
    },
    yAxis: [{
        type: 'value',
		min: 'dataMin',
		max: 'dataMax',
		axisLabel: {                   
                formatter: function (value, index) {           
                 return value.toFixed(2);      
                 }    
		 },
	    axisLine: {
          show: true,
          symbol: ['none', 'none'],
		}
		
    },
	{
        type: 'value',
		min: 'dataMin',
		max: 'dataMax',
		axisLabel: {                   
                formatter: function (value, index) {           
                 return data2.length > 0 && g_percent ? value.toFixed(2) + '%' : value.toFixed(2);      
                 }    
		 },
	    axisLine: {
          show: true,
          symbol: ['none', 'none'],
		  lineStyle: count2 == 0 ? null : {
                    color: 'red' // 右侧 y 轴的颜色设置为红色
          }
		}
		
    }],
    series: [
        {
            name:name,
            type:'line',
    
			showSymbol: count < 40, 
			 yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: 'rgb(0, 0, 255)'
                }
            },

            data: data
        },
		data2.length  ? 
        {
            name: title2 == '' ? '%' : title2 ,
            type:'line',
    
			showSymbol: count < 40, 
			 yAxisIndex: 1,
             itemStyle: {
                normal: {
                    color: g_percent ? 'rgb(0, 0, 255)' : 'rgb(255, 0, 0)'
                }
            },

            data: data2
        } :         {
            name:name,
            type:'line',
    
			showSymbol: count < 40, 
			 yAxisIndex: 1,
            itemStyle: {
                normal: {
                    color: 'rgb(0, 0, 255)'
                }
            },

            data: data
        }
    ]
};

if (option && typeof option === "object") {
    myChart.setOption(option, true);
}
}


var app = {};
option = null;

var date = [];

var data = [];
var data2 = [];
var data3 = [];
var data4 = [];
var data5 = [];
var data6 = [];
RefreshChart();
function OnSize(){
myChart.resize();
}


       </script>
   </body>
</html>