<!DOCTYPE html>
<html style="height: 100%">
   <head>
       <meta charset="utf-8">
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
   </head>
   <body style="height: 100%; margin: 0">
       <div id="container" style="height: 100%"></div>
       <script type="text/javascript" src="echarts.common.min.js"></script>

       <script type="text/javascript">
	   
function strToDate(str)
{
  var arys= new Array();
  arys=str.split('-');
  var newDate=new Date(arys[0],arys[1],arys[2]); 
  return newDate;
  
}

function MaxIndex(arr)
{
	var max = -50000;
	var index = -1;
	for(var i=0;i<arr.length;i++){ 
	  if(max<arr[i]){
		max=arr[i];
		index = i;
	  }
	}
	return index;
}

function MinIndex(arr)
{
	var max = 500000000;
	var index = -1;
	for(var i=0;i<arr.length;i++){ 
	  if(max>arr[i]){
		max=arr[i];
		index = i;
	  }
	}
	return index;
}

var dom = document.getElementById("container");
var myChart = echarts.init(dom);

function RefreshChart()
{

}
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1) + min);
}

var app = {};
option = null;

var date = [];

var data = [];
var data2 = [];
var data3 = [];
var data4 = [];

var count = external.GetCapitalCount();
var title = external.GetTitle();
var title2 = external.GetTitleN(2);
var title3 = external.GetTitleN(3);
var count2 = external.GetDataCount(2);
var count3 = external.GetDataCount(3);
var count4 = external.GetDataCount(4);

if(title2 == "")
	title2 = '资产';
if(title3 == "")
	title3 = '资产%';
	
var indexBase = 1;
var singleMonth = title.indexOf('单月') >= 0 ;
if (singleMonth)
	title3 = '盈利';
for (var i = 0; i < count; i++) {
    //var now = new Date(external.GetCapitalDate(i));
	//var now = strToDate(external.GetCapitalDateS(i));
	var now = external.GetCapitalDateS(i);
    date.push(now);
	if (singleMonth)
	{
		if (i == 0)
			data.push(external.GetCapital(i));
		else
			data.push(external.GetCapital(i) - external.GetCapital(i-1));
			
		//if (data[i] < 1)
		//	data[i] = getRandomInt(8000, 15000);
	}
	else
		data.push(external.GetCapital(i));

	if (count2 > 0)
	{
		data2.push(external.GetData(2,i))
		if (count3 > 0)
			data3.push(external.GetData(3,i))
		if (count4 > 0)
			data4.push(external.GetData(4,i))
	}
	else
	{
		if (i == 0){
			
			data2.push(0);
			
		}
		else{
			var change = ((data[i] -  data[0]) / data[0])*100;
			data2.push(change);
			
		}
	}
}

var data2minIndex = MinIndex(data2);
var data2maxIndex = MaxIndex(data2);
var data2min = data2[data2minIndex];
var data2max = data2[data2maxIndex];


var dataminIndex = MinIndex(data);
var datamaxIndex = MaxIndex(data);
var datamin = data[dataminIndex];
var datamax = data[datamaxIndex];


var dataminTotal = Math.min(data2min, datamin);
var datamaxTotal = Math.max(data2max, datamax);
var monthchart = title.indexOf('月线') > 0 ;
var pct = title3.indexOf('%') > 0 ;


option = {
    tooltip: {
        trigger: 'axis',
		    formatter:function(params)  
        {  
           var relVal = params[0].name;  
           for (var i = 0, l = params.length; i < l; i++) {  
				if (i == 0)
					relVal += '<br/>' + params[i].seriesName + ' : ' + params[i].value.toFixed(2);  
				else
					relVal += '<br/>' + params[i].seriesName + ' : ' + params[i].value.toFixed(2);  
            }  
           return relVal;  
        },
        confine: true,  // 限制在图表区域内
		position: function(point, params, dom, rect, size) 
		{
			var posX = point[0];
			var posY = point[1];
			var viewWidth = size.viewSize[0];
			var viewHeight = size.viewSize[1];
			var boxWidth = size.contentSize[0];
			var boxHeight = size.contentSize[1];

			// 获取当前点的索引
			var dataIndex = params[0].dataIndex;
			var totalDataCount = params[0].data.length;
			
			// 如果是最后两个点，将tooltip显示在左侧
			if (dataIndex >= totalDataCount - 2) {
				
				posX = posX - boxWidth;
			} else {
				// 调整tooltip位置，确保其不会超出右边界
				if (posX + boxWidth > viewWidth) {
					posX = posX - boxWidth;
				}
			}

			// 调整tooltip位置，确保其不会超出上/下边界
			if (posY + boxHeight > viewHeight) {
				posY = viewHeight - boxHeight;
			} else if (posY < 0) {
				posY = 0;
			}

			return [posX, posY];
        }
    },
    title: {
        left: 'center',
        text: title
    },

	legend: {
        data: [title2 == '盈利' ? null : title2, count4 > 0 ? null : null, title3, count3 > 0 ? '总盈利%' : null],
        right: '4%',
		 show: true 

    },
	grid: {
		left: '70',
		right: '70'
	},
	toolbox: {
		x:'left',
		left:"4%",
        feature: {

			    myTool1: {
                show: true,
                title: '导出数据',
                icon: 'path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891',
                onclick: function (){
                    external.Command(0);
                }
				}
        }
    },
    xAxis: {
        type: 'category',
        boundaryGap: monthchart ? true : false,
        data: date
    },
    yAxis: [{
        type: 'value',
		min: pct ? 'dataMin' : dataminTotal,
		max: pct ? 'dataMax' : datamaxTotal,
		//min: yaxismin,
		//max: yaxismax,
		axisLabel: {                   
                formatter: function (value, index) {           
                 return value.toFixed(0);      
                 }
				 
		 },
	    axisLine: {
          show: true,
          symbol: ['none', 'none'],
		}
		
    },
	{
	   type: 'value',
		min: pct ? 'dataMin' : dataminTotal,
		max: pct ? 'dataMax' : datamaxTotal,
		axisLabel: {                   
                formatter: function (value, index) {           
                 return  title3.indexOf('%') > 0 && !singleMonth ?  value.toFixed(2) + '%' : value.toFixed(0);      
                 } ,
				color:  pct ? function (value, index) {
						return value == 0 ? null : value < 0 ? 'green' : 'red' ;
				}	: null			 
		 },
	    axisLine: {
          show: true,
          symbol: ['none', 'none'],
		}
		
    }],
  visualMap: !monthchart && pct && count3 == 0 ? {
              show: false,
              //lt（小于），gt（大于），lte（小于等于），gte（大于等于）
              pieces: pct ? [{
                  lte: 0,
				  gte: data2min,
                  color: 'green',
              }, {
                  gt: 0,
				  lte: data2max,
                  color: 'red',
              }
              ] : [],

			  
          } : null,
    series: [
	    !monthchart ?
        {
            name:title2,
            type: 'line',
    
			showSymbol: count < 40, 
			 yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: count3 > 0 ? 'rgb(128, 0, 0)' : pct ? 'rgb(255, 0, 0)' : 'rgb(0, 0, 255)'
                }
            },

            data: data
        } : null,
		count4 > 0 && !monthchart ? 
		{
            name: '总盈利',
            type:'line',
    
			showSymbol: count < 40, 
			 yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: 'rgb(255, 128, 0)'
                }
            },

            data: data4
        } : null,
		count3 > 0 && !singleMonth? 
		{
            name: '总盈利%',
            type:'line',
    
			showSymbol: count < 40, 
			 yAxisIndex: 1,
            itemStyle: {
                normal: {
                    color: 'rgb(248, 168, 172)'
                }
            },
            areaStyle: monthchart ? null : {
               
            },
			label: monthchart ? {
                show: true,
				position: "top",
                formatter: function(params) {
					var str ;
					if (data4[params.dataIndex] == 0)
						str = '';
					else{
						str = data4[params.dataIndex] < 10000 ? '' + data4[params.dataIndex].toFixed(0) : '' + (data4[params.dataIndex]/10000).toFixed(2) +'万';
					}
                    return data3[params.dataIndex] == 0 ? '' : '' + data3[params.dataIndex].toFixed(2) + '% \n'  + str;
                }
            } : null,
            data: data3
        } : null,
        {
            name: title3,
            type:monthchart ? 'bar' : 'line',
    
			showSymbol: count < 40, 
			 yAxisIndex: 1,
            itemStyle: {
                normal: {
                    color: 'rgb(255, 0, 0)'
                }
            },
            areaStyle: monthchart ? null : {
               
            },
			label: monthchart ? {
                show: true,
				position: "top",
                formatter: function(params) {
					var str ;
					if (singleMonth)
					{
						str = data[params.dataIndex] < 10000 ? '' + data[params.dataIndex].toFixed(0) : '' + (data[params.dataIndex]/10000).toFixed(2) +'万';
						return str;
					}
					if (data[params.dataIndex] == 0)
						str = '';
					else{
						str = data[params.dataIndex] < 10000 ? '' + data[params.dataIndex].toFixed(0) : '' + (data[params.dataIndex]/10000).toFixed(2) +'万';
					}
                    return  data2[params.dataIndex] == 0 ? '' : '' + data2[params.dataIndex].toFixed(2) + '% \n'  + str;
                }
            } : null,
            data: singleMonth ? data : data2
        }
    ]
};

function OnSize(){
myChart.resize();
}

if (option && typeof option === "object") {
    myChart.setOption(option, true);
}
       </script>
   </body>
</html>