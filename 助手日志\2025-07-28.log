2025-07-28 09:23:35 股票助手3.3.8.4 启动####################################### 
2025-07-28 09:23:43 !!!!!!!!!!!159711 vs  try 1

2025-07-28 09:23:43 !!!!!!!!!!!159711 vs  try 2

2025-07-28 09:23:43 !!!!!!!!!!!159711 vs  try 3

2025-07-28 09:24:02 C:\Users\<USER>\Documents\p 资金股份查询.txt use default 
2025-07-28 09:24:02 File not exists C:\Users\<USER>\Documents\p 资金股份查询.txt 
2025-07-28 09:24:02 File not exists C:\Users\<USER>\Documents\table.xls 
2025-07-28 09:24:02 C:\Users\<USER>\Documents\table.xls not position find
2025-07-28 09:24:02 重连行情服务器:
2025-07-28 09:24:02 正在初始化行情接口...TdxHqApi.dll
2025-07-28 09:24:02 尝试连接 通达信深圳双线主站1:110.41.147.114:7709 
2025-07-28 09:24:02 连接 通达信深圳双线主站1:110.41.147.114:7709 成功 
2025-07-28 09:24:02 初始化行情接口完成...
2025-07-28 09:24:04 get t1
2025-07-28 09:24:04 20秒后自动开始自动交易
2025-07-28 09:24:04 已备份重要文件于 E:\助手\HY\StockOrder\data\backup
2025-07-28 09:24:04 读取到最新总资产 2783 , 超过了上次读取的3162 波动 11.99% 超出了 11.00%范围, 可能资产读取出错。如果是正常的,可以修改总资产波动范围设置；如数据有误,关闭软件后修改 总资产记录文件或删除文件再重启。资产文件是 E:\助手\HY\StockOrder\Money\网上股票交易系统5.0\资产曲线.txt, 如果你使用自定义数据, 还需要修改或删除自定义数据里的资产数据
2025-07-28 09:24:04 成功连接通达信行情接口
2025-07-28 09:25:02 64 alert E:\dzh2\dzh2.exe
2025-07-28 09:30:05 File not exists E:\助手\HY\StockOrder\p money.txt 
2025-07-28 09:30:05 File not exists C:\Users\<USER>\Documents\table.xls 
2025-07-28 09:30:05 C:\Users\<USER>\Documents\table.xls not position find
2025-07-28 09:30:14 C:\Users\<USER>\Documents\p 资金股份查询.txt use default 
2025-07-28 09:30:14 File not exists C:\Users\<USER>\Documents\p 资金股份查询.txt 
2025-07-28 09:30:14 File not exists C:\Users\<USER>\Documents\table.xls 
2025-07-28 09:30:14 C:\Users\<USER>\Documents\table.xls not position find
2025-07-28 09:39:23 Could not map view of file (0).

2025-07-28 09:39:23 Could not map view of file (0).

2025-07-28 09:39:24 命令行下单 买入 buy 600117 3.89 100 -notip (完成 1219毫秒)
2025-07-28 09:39:24 buy spend = 1235
2025-07-28 09:40:00 C:\Users\<USER>\Documents\p 资金股份查询.txt use default 
2025-07-28 09:40:00 File not exists C:\Users\<USER>\Documents\p 资金股份查询.txt 
2025-07-28 09:40:00 File not exists C:\Users\<USER>\Documents\table.xls 
2025-07-28 09:40:00 C:\Users\<USER>\Documents\table.xls not position find
2025-07-28 09:46:22 C:\Users\<USER>\Documents\p 资金股份查询.txt use default 
2025-07-28 09:46:22 File not exists C:\Users\<USER>\Documents\p 资金股份查询.txt 
2025-07-28 09:46:22 File not exists C:\Users\<USER>\Documents\table.xls 
2025-07-28 09:46:22 C:\Users\<USER>\Documents\table.xls not position find
