#coding=utf-8
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
from threading import Thread, Lock
import time, datetime, traceback, sys
from xtquant import xtdata
import socket
import re

import logging

port = 9888  # initiate port no above 1024
#now we will Create and configure logger
log_file = "QuoteServer.log"
if len(sys.argv) > 2:
    log_file = sys.argv[2]
if len(sys.argv) > 3:
    port = int(sys.argv[3])
logging.basicConfig(filename=log_file,
					format='%(asctime)s %(message)s',
					filemode='a')

#Let us Create an object
logger=logging.getLogger()
#Now we are going to Set the threshold of logger to DEBUG
logger.setLevel(logging.INFO)
path = 'D:\\国金证券QMT交易端\\userdata_mini'
if len(sys.argv) > 1:
    logger.info(sys.argv[1])
    path = sys.argv[1]

logger.info(log_file)
#some messages to test
#logger.debug("This is just a harmless debug message")
#logger.info("This is just an information for you")
#logger.warning("OOPS!!!Its a Warning")
#logger.error("Have you try to divide a number by zero")
#logger.critical("The Internet is not working....")

lock = Lock()
conn = None
sub_id = 0
index_list = ["000001.SH", "399001.SZ", "399005.SZ", "399006.SZ"]
a_list = []
ag_list = []

try:
    ag_list = xtdata.get_stock_list_in_sector('沪深A股')
except Exception as e:
    logger.info(e)

etf_list  = xtdata.get_stock_list_in_sector('沪深基金')
if len(etf_list) == 0:
    etf_list  = xtdata.get_stock_list_in_sector('沪深ETF')
huigou_list  = xtdata.get_stock_list_in_sector('沪深回购')
if len(huigou_list) == 0:
    huigou_list  = xtdata.get_stock_list_in_sector('沪深逆回购')
zhuan_list  = xtdata.get_stock_list_in_sector('沪深转债')
if len(zhuan_list) == 0:
    zhuan_list  = xtdata.get_stock_list_in_sector('沪深可转债')
	
bj_list  = xtdata.get_stock_list_in_sector('京市A股')


logger.info(bj_list)
	
a_list = ag_list + etf_list + index_list + huigou_list + zhuan_list

# 将列表转换为集合
set1 = set(bj_list)
set2 = set(a_list)

# 判断 set1 的所有元素是否都在 set2 中
is_all_elements_included = set1 <= set2
#logger.info(is_all_elements_included)  # 如果 list1 的所有元素都在 list2 中，输出 True
if bj_list is not None and is_all_elements_included == False:
    a_list = a_list + bj_list

#可转债
r1 = re.compile("^11[0-9][0-9]{3}$")
r2 = re.compile("^12[0-9][0-9]{3}$")
#债券回购
r3 = re.compile("^20[1-5][0-9]{3}$")
r4 = re.compile("^131[0-9]{3}$")


def send(conn, data):
  lock.acquire()
  try:
    conn.send(data)
  except Exception as e:
    lock.release()
    raise e
  lock.release()

def on_quote(data):
    global conn

    strData = "quote "
    i = 0
    for stock in data:
      listCmd = stock.split(".")
      stock_code = listCmd[0]
      mkt_code = listCmd[1]
      #if mkt_code == "BJ" or stock_code == "831152":
      #  logger.info(stock)
      if stock not in a_list and not (r1.match(stock_code) or r2.match(stock_code) or r3.match(stock_code)  or r4.match(stock_code) ):
        continue
      quote_data = data[stock]
      str1 = "{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},{21},{22},{23},{24},{25},{26},{27},{28},{29},{30}".format(
        stock,
        quote_data['open'],#1
        quote_data['lastClose'],#2
        quote_data['lastPrice'],  # 3
        quote_data['high'],  # 4
        quote_data['low'],  # 5
        0,  # 6
        0,  # 7
        quote_data['volume'],  # 8
        quote_data['amount'],  # 9
        quote_data['bidVol'][0],  # 10
        quote_data['bidPrice'][0],  # 11
        quote_data['bidVol'][1],  # 12
        quote_data['bidPrice'][1],  # 13
        quote_data['bidVol'][2],  # 14
        quote_data['bidPrice'][2],  # 15
        quote_data['bidVol'][3],  # 16
        quote_data['bidPrice'][3],  # 17
        quote_data['bidVol'][4],  # 18
        quote_data['bidPrice'][4],  # 19
        quote_data['askVol'][0],  # 20
        quote_data['askPrice'][0],  # 21
        quote_data['askVol'][1],  # 22
        quote_data['askPrice'][1],  # 23
        quote_data['askVol'][2],  # 24
        quote_data['askPrice'][2],  # 25
        quote_data['askVol'][3],  # 26
        quote_data['askPrice'][3],  # 27
        quote_data['askVol'][4],  # 28
        quote_data['askPrice'][4],  # 29
        quote_data['time'] ,  # 30
        )
      strData = strData + str1 + "\n"



    try:
      if conn is not None:
        strData = strData[0:-1] + "\r\n"
        strData = strData + "\r\n"
        send(conn,  strData.encode())
    except Exception as e:
      conn = None
      print(e)


def get_tick():

    data = xtdata.get_full_tick(a_list)
    strData = "quote "
    for stock in data:
        # print(stock)
        quote_data = data[stock]

        listCmd = stock.split(".")
        stock_code = listCmd[0]
        if stock not in a_list and not (
                    r1.match(stock_code) or r2.match(stock_code) or r3.match(stock_code) or r4.match(stock_code)):
            continue
        str1 = "{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16},{17},{18},{19},{20},{21},{22},{23},{24},{25},{26},{27},{28},{29},{30}".format(
            stock,
            quote_data['open'],  # 1
            quote_data['lastClose'],  # 2
            quote_data['lastPrice'],  # 3
            quote_data['high'],  # 4
            quote_data['low'],  # 5
            0,  # 6
            0,  # 7
            quote_data['volume'],  # 8
            quote_data['amount'],  # 9
            quote_data['bidVol'][0],  # 10
            quote_data['bidPrice'][0],  # 11
            quote_data['bidVol'][1],  # 12
            quote_data['bidPrice'][1],  # 13
            quote_data['bidVol'][2],  # 14
            quote_data['bidPrice'][2],  # 15
            quote_data['bidVol'][3],  # 16
            quote_data['bidPrice'][3],  # 17
            quote_data['bidVol'][4],  # 18
            quote_data['bidPrice'][4],  # 19
            quote_data['askVol'][0],  # 20
            quote_data['askPrice'][0],  # 21
            quote_data['askVol'][1],  # 22
            quote_data['askPrice'][1],  # 23
            quote_data['askVol'][2],  # 24
            quote_data['askPrice'][2],  # 25
            quote_data['askVol'][3],  # 26
            quote_data['askPrice'][3],  # 27
            quote_data['askVol'][4],  # 28
            quote_data['askPrice'][4],  # 29
            time.time() * 1000,  # 30
        )

        strData = strData + str1 + "\n"

    try:
        strData = strData[0:-1] + "\r\n"
        strData = strData + "\r\n"
        #logger.info(strData)
        if conn is not None:
            # print(strData)
            # append_quote(strData)
            send(conn, strData.encode())
    except Exception as e:
        logger.info(e)

def get_full_tick(stock_str):
    global  sub_id;
    try:
        if stock_str is None or len(stock_str) == 0:
            xtdata.subscribe_whole_quote(["SH", "SZ", "BJ"], callback=on_quote)
        else:
            if len(stock_str) > 0:
                a_list = stock_str.split(",")
                for index in index_list:
                    if index not in a_list:
                        a_list.append(index)
                if sub_id > 0:
                    # print("取消全推订阅")
                    xtdata.unsubscribe_quote(sub_id)

                sub_id = xtdata.subscribe_whole_quote(a_list, callback=on_quote)
                print("点播订阅:", a_list)
                logger.info(a_list)
        #get_tick()
    except Exception as e:
        logger.info(e)
def server():
    # get the hostname
    host = "127.0.0.1" #socket.gethostname()


    server_socket = socket.socket()  # get instance
    # look closely. The bind() function takes tuple as argument
    server_socket.bind((host, port))  # bind host address and port together

    # configure how many client the server can listen simultaneously
    print("server running ")
    logger.info("quote server listen " + str(port))
    server_socket.listen(1)
    global conn
    conn, address = server_socket.accept()  # accept new connection
    print("Connection from: " + str(address))
    while True:
        # receive data stream. it won't accept data packet greater than 1024 bytes
        try:
          data = conn.recv(1024).decode()
          if not data:
              #break
              #server_socket.listen(1)
              print("None data, relisten")
              logger.error("None data, reaccept ")
              conn, address = server_socket.accept()  # accept new connection

          #print("from  user: " + str(data))
          listLine = str(data).split("\r\n")
          for line in listLine:
            listCmd = line.split(" ")
            cmd = listCmd[0]
            if cmd == "heart" :
              send(conn, data.encode())   # send data to the client
            elif cmd == "quote" :
              get_full_tick(None if len(listCmd) < 2 else listCmd[1])
            elif cmd == "SwitchSub" :
              pass#A.SwitchSub = str_to_bool(listCmd[1])
              pass#A.SwitchingSub = True


        except socket.error as e:
          print( e )
          logger.error(e)
          #logger.error("reaccept ")
          #server_socket.listen(1)
          conn, address = server_socket.accept()  # accept new connection


    conn.close()  # close the connection
    logger.info("server exit ")
    print("server exit ")


import os
import xml.etree.ElementTree as ET
import codecs


def check_m_bQmtSubscribe():
    """
    读取当前脚本上一级目录中的 config.xml 文件中的 m_bQmtSubscribe 值，并返回其布尔值。

    Returns:
    - bool: 返回 m_bQmtSubscribe 的布尔值，True 表示为1，False 表示为0或其他值。
      如果出现错误或找不到 m_bQmtSubscribe 属性，则返回 None。
    """
    try:
        # 获取当前脚本的文件路径
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 上一级目录路径
        parent_dir = os.path.dirname(script_dir)

        # XML 文件路径
        xml_file = os.path.join(parent_dir, 'StockOrder_Setting.xml')

        # 使用 codecs 模块以指定编码打开文件
        with codecs.open(xml_file, 'r', encoding='GB2312') as f:
            xml_content = f.read()

        # 解析 XML 文件内容
        root = ET.fromstring(xml_content)

        # 找到 Setting 标签下的 Global 元素
        global_elem = root.find('Global')

        # 获取 m_bQmtSubscribe 的值并转换为布尔值
        m_bQmtSubscribe_value = global_elem.get('m_bQmtSubscribe')

        # 转换为布尔值
        if m_bQmtSubscribe_value == '1':
            return True
        else:
            return False

    except Exception as e:
        print(f"Error reading XML file: {str(e)}")
        return False




if __name__ == "__main__":
    print("quote server start")
    logger.info("quote server start ")

    result = check_m_bQmtSubscribe()
    if result:
        logger.info("点播模式")
        print("点播模式")
    else:
        sub_id = xtdata.subscribe_whole_quote(["SH", "SZ", "BJ"], callback=on_quote)
        logger.info("全推模式")
        print("全推模式")
    server()

