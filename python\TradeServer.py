#coding=utf-8
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
from threading import Thread, Lock
import socket
import  json
import time
import  sys
import random

#importing the module
import logging

#now we will Create and configure logger
log_file = "TradeServer.log"
port = 9876
if len(sys.argv) > 2:
    log_file = sys.argv[2]

if len(sys.argv) > 3:
    port = int(sys.argv[3])

logging.basicConfig(filename=log_file,
					format='%(asctime)s %(message)s',
					filemode='a')

#Let us Create an object
logger=logging.getLogger()
#Now we are going to Set the threshold of logger to DEBUG
logger.setLevel(logging.INFO)
path = 'D:\\国金证券QMT交易端\\userdata_mini'
if len(sys.argv) > 1:
    logger.info(sys.argv[1])
    path = sys.argv[1]

logger.info(log_file)
#some messages to test
#logger.debug("This is just a harmless debug message")
#logger.info("This is just an information for you")
#logger.warning("OOPS!!!Its a Warning")
#logger.error("Have you try to divide a number by zero")
#logger.critical("The Internet is not working....")



# session_id为会话编号，策略使用方对于不同的Python策略需要使用不同的会话编号
session_id = random.randint(10000, 99999)
xt_trader = XtQuantTrader(path, session_id)
lock = Lock()
conn = None
account_map = {}

def send(conn, data):
  lock.acquire()
  try:
    if conn:
        conn.send(data)
  except Exception as e:
    lock.release()
    raise e
  lock.release()

class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        print("connection lost")
        logger.info("connection lost ")
        send(conn, ("connectionlost " +  "connectionlost" + "\r\n").encode())
    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        print("on order callback:")
        print(order.stock_code, order.order_status, order.order_sysid)
        if order.strategy_name == "助手人工":
          if order.order_status == 50:
            send(conn, ("message " + order.stock_code + " 委托已发出 "  + "\r\n").encode())
          elif  order.order_status == 54:
            send(conn, ("message " + order.stock_code + " 委托已撤销 " + "\r\n").encode())

        str_position = "order_push 资金账号	股票代码	数量	委托编号	成交均价	委托类型	成交数量	委托状态	状态描述	策略名称	委托时间	柜台编号	委托备注\n";

        position = order
        listCmd = position.stock_code.split(".")
        stock_code = listCmd[0]

        str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}	{10}	{11}	{12}\r\n".format(
            position.account_id,
            stock_code,
            position.order_volume,
            position.order_id,
            position.traded_price,
            position.order_type,
            position.traded_volume,
            position.order_status,
            position.status_msg,
            position.strategy_name,
            position.order_time,
            position.order_sysid,
            position.order_remark)

        str_position = str_position + str1
        send(conn, str_position.encode())

        logger.info("委托变动推送 ")
        logger.info(str_position)

    def on_stock_asset(self, asset):
        """
        资金变动推送
        :param asset: XtAsset对象
        :return:
        """
        print("on asset callback")
        print(asset.account_id, asset.cash, asset.total_asset)
    def on_stock_trade(self, trade):
        """
        成交变动推送
        :param trade: XtTrade对象
        :return:
        """
        print("on trade callback")
        str_position = "trade_push 资金账号	股票代码	委托编号	委托类型	成交均价	成交数量	成交编号	成交时间	策略名称	柜台编号	委托备注\n";
        position = trade
        listCmd = position.stock_code.split(".")
        stock_code = listCmd[0]

        str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}	{10}\r\n".format(position.account_id,
                                                                                     stock_code,
                                                                                     position.order_id,
                                                                                     position.order_type,
                                                                                     position.traded_price,
                                                                                     position.traded_volume,
                                                                                     position.traded_id,
                                                                                     position.traded_time,
                                                                                     position.strategy_name,
                                                                                     position.order_sysid,
                                                                                     position.order_remark)

        str_position = str_position + str1
        send(conn, str_position.encode())
        if position.strategy_name == "助手人工" or position.strategy_name.find("闪电") >= 0:
          send(conn, ("message " + stock_code + " 成交 " + str(position.traded_volume) + "股 价格 " + str(position.traded_price) + "\r\n").encode())

        logger.info("成交变动推送 ")
        logger.info(str_position)
        print(str_position)

    def on_stock_position(self, position):
        """
        持仓变动推送
        :param position: XtPosition对象
        :return:
        """
        print("on position callback")
        print(position.stock_code, position.volume)
        listCmd = position.stock_code.split(".")
        stock_code = listCmd[0]
        str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}\r\n".format(position.account_id, stock_code,
                                                                                      position.volume,
                                                                                      position.can_use_volume,
                                                                                      position.on_road_volume,
                                                                                      position.frozen_volume,
                                                                                      position.market_value,
                                                                                      position.open_price,
                                                                                      position.account_type)
        send(conn, ("positionchg " + str1).encode())
    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        print("on order_error callback")
        print(order_error.order_id, order_error.error_id, order_error.error_msg)
        logger.info("on_order_error  %s, %d,  %d", order_error.error_msg, order_error.order_id, order_error.error_id)
        strError = order_error.order_remark + '#####' + order_error.error_msg
        if order_error.strategy_name == "助手人工" or order_error.strategy_name.find("闪电") >= 0:
          send(conn, ("message " + order_error.error_msg + "\r\n").encode())

        send(conn, ("order_err " + strError+ "\r\n").encode())

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        print("on cancel_error callback")
        print(cancel_error.order_id, cancel_error.error_id, cancel_error.error_msg)
        logger.info("on_cancel_error  %s, %d,  %d", cancel_error.order_id, cancel_error.error_id, cancel_error.error_msg)
    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        print("on_order_stock_async_response")
        logger.info("on_order_stock_async_response  %s, %d,  %d", response.account_id, response.order_id, response.seq)
        print(response.account_id, response.order_id, response.seq)


    def on_account_status(self, status):
        """
        :param response: XtAccountStatus 对象
        :return:
        """
        print("on_account_status")
        print(status.account_id, status.account_type, status.status)

def query_position(strAcc):
  #print("query positions:")
  str_position = "position 资金账号	证券代码	当前拥股	可用余额	在途股份	冻结数量	市值	成本价	账号类型\n";
  have = False
  for item in account_map.items():
    key = item[0]
    acc = item[1]
    if strAcc is None or strAcc == "" or strAcc == key:
      positions = xt_trader.query_stock_positions(acc)
      #print("positions count :", len(positions))
      if positions:
        for i, position in enumerate(positions):
          #print("{0} {1} {2}".format(position.account_id, position.stock_code, position.volume))
          listCmd = position.stock_code.split(".")
          stock_code = listCmd[0]
          if (i == len(positions) - 1):
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}\n".format(position.account_id, stock_code,
                                                                            position.volume, position.can_use_volume,
                                                                            position.on_road_volume, position.frozen_volume,
                                                                            position.market_value, position.open_price,position.account_type)
          else:
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}\n".format(position.account_id, stock_code,
                                                                          position.volume, position.can_use_volume,
                                                                          position.on_road_volume, position.frozen_volume,
                                                                          position.market_value, position.open_price,position.account_type)

          str_position = str_position + str1
          have = True;
  if have:
    str_position = str_position[0:len(str_position)-1]
    str_position = str_position + "\r\n"
    send(conn, str_position.encode())


def query_stock_asset(strAcc):
  #print("query stock_asset:")
  str_position = "asset 资金账号	可用资金	最新市值	总资产	账号类型\n";
  count = 0;
  for item in account_map.items():
    key = item[0]
    acc = item[1]
    if strAcc is None or strAcc == "" or strAcc == key:
      position = xt_trader.query_stock_asset(acc)
      if position:
        if count == len(account_map)-1:
          str1 = "{0}	{1}	{2}	{3}	{4}\r\n".format(position.account_id, position.cash, position.market_value, position.total_asset, position.account_type)
        else:
          str1 = "{0}	{1}	{2}	{3}	{4}\n".format(position.account_id, position.cash, position.market_value,
                                                      position.total_asset, position.account_type)
        str_position = str_position + str1
        count = count + 1
  if count > 0:
    send(conn, str_position.encode())


def query_credit_detail(strAcc):
  #print("query_credit_detail:")
  str_position = "credit 资金账号	可用资金	最新市值	总资产	净资产	可用保证金	担保比率	融资信用额度	融券信用额度	账号类型\n";
  have = False;
  for item in account_map.items():
    key = item[0]
    acc = item[1]
    if (strAcc is None or strAcc == "" or strAcc == key) and acc.account_type == xtconstant.CREDIT_ACCOUNT:
      positions = xt_trader.query_credit_detail(acc)
      #print("credit count :", len(positions))
      if positions:
        for i, position in enumerate(positions):
          if (i == len(positions) - 1):
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}\r\n".format(position.account_id,
                                                                                 position.m_dAvailable,
                                                                                 position.m_dMarketValue,
                                                                                 position.m_dBalance,
                                                                                 position.m_dAssureAsset,
                                                                                 position.m_dEnableBailBalance,
                                                                                 position.m_dPerAssurescaleValue*100,
                                                                                 0,
                                                                                 0,
                                                                                 position.account_type)
          else:
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}\n".format(position.account_id, position.m_dAvailable, position.m_dMarketValue, position.m_dBalance, position.m_dAssureAsset, position.m_dEnableBailBalance, position.m_dPerAssurescaleValue*100, 0, 0, position.account_type)

          str_position = str_position + str1
          have = True;
  if have:
    send(conn, str_position.encode())

def query_order(strAcc):
  #print("query orders:")
  str_position = "order 资金账号	股票代码	数量	委托编号	成交均价	委托类型	成交数量	委托状态	状态描述	策略名称	委托时间	柜台编号	委托备注\n";
  have = False
  for item in account_map.items():
    key = item[0]
    acc = item[1]
    if strAcc is None or strAcc == "" or strAcc == key:
      positions = xt_trader.query_stock_orders(acc, True)
      if positions:
        for i, position in enumerate(positions):
          listCmd = position.stock_code.split(".")
          stock_code = listCmd[0]
          if (i == len(positions) - 1):
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}	{10}	{11}	{12}\r\n".format(position.account_id,
                                                                                     stock_code,
                                                                                     position.order_volume,
                                                                                     position.order_id,
                                                                                     position.traded_price,
                                                                                     position.order_type,
                                                                                     position.traded_volume,
                                                                                     position.order_status,
                                                                                     position.status_msg,
                                                                                     position.strategy_name,
                                                                                     position.order_time,
                                                                                     position.order_sysid,
                                                                                     position.order_remark )
          else:
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}	{10}	{11}	{12}\n".format(position.account_id,
                                                                                 stock_code,
                                                                                 position.order_volume,
                                                                                 position.order_id,
                                                                                 position.traded_price,
                                                                                 position.order_type,
                                                                                 position.traded_volume,
                                                                                 position.order_status,
                                                                                 position.status_msg,
                                                                                 position.strategy_name,
                                                                                 position.order_time,
                                                                                 position.order_sysid,
                                                                                 position.order_remark )
          str_position = str_position + str1
          have = True;
  if have:
    send(conn, str_position.encode())

def query_trade(strAcc):
  #print("query trade:")
  str_position = "trade 资金账号	股票代码	委托编号	委托类型	成交均价	成交数量	成交编号	成交时间	策略名称	柜台编号	委托备注\n";
  have = False
  for item in account_map.items():
    key = item[0]
    acc = item[1]
    if strAcc is None or strAcc == "" or strAcc == key:
      positions = xt_trader.query_stock_trades(acc)
      if positions:
        for i, position in enumerate(positions):
          listCmd = position.stock_code.split(".")
          stock_code = listCmd[0]
          if (i == len(positions) - 1):
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}	{10}\r\n".format(position.account_id,
                                                                                     stock_code,
                                                                                     position.order_id,
                                                                                     position.order_type,
                                                                                     position.traded_price,
                                                                                     position.traded_volume,
                                                                                     position.traded_id,
                                                                                 position.traded_time,
                                                                                 position.strategy_name,
                                                                                 position.order_sysid,
                                                                                 position.order_remark)
          else:
            str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}	{8}	{9}	{10}\n".format(position.account_id,
                                                                                 stock_code,
                                                                                 position.order_id,
                                                                                 position.order_type,
                                                                                 position.traded_price,
                                                                                 position.traded_volume,
                                                                                 position.traded_id,
                                                                                 position.traded_time,
                                                                                 position.strategy_name,
                                                                                 position.order_sysid,
                                                                                 position.order_remark)
          str_position = str_position + str1
          have = True
  if have:
    send(conn, str_position.encode())

def query_npl(strAcc):
  #print("query stock_asset:")

  str_position = "ipo_npl 资金账号	市场	申购额度\n";
  count = 0;
  for item in account_map.items():
    key = item[0]
    acc = item[1]
    if strAcc is None or strAcc == "" or strAcc == key:
      positions = xt_trader.query_new_purchase_limit(acc)
      for i, position in enumerate(positions):
        str1 = "{0}	{1}	{2}\n".format(acc.account_id, position['type'] , position['number'] )
        str_position = str_position + str1
        count = count + 1

  if count > 0:
    str_position = str_position[0:len(str_position)-1]
    str_position = str_position + "\r\n"
    send(conn, str_position.encode())

def query_ipo(strAcc):
  #print("query stock_asset:")

  str_position = "ipo_data 股票代码	市场	类型	日期	价格  名称  最少申购    最大申购\n";
  count = 0;
  ipoData = xt_trader.query_ipo_data()  # 返回新股新债信息
  if ipoData:
      for key, position in ipoData.items():
          # print("{0} {1} {2}".format(position.account_id, position.stock_code, position.volume))
          listCmd = key.split(".")
          stock_code = listCmd[0]
          mk = listCmd[1]
          count = count + 1
          str1 = "{0}	{1}	{2}	{3}	{4}	{5}	{6}	{7}\n".format(stock_code,
                                                                                        mk,
                                                                                        position['type'],
                                                                                        position['purchaseDate'],
                                                                                        position['issuePrice'],
                                                                                        position['name'],
                                                                                        position['minPurchaseNum'],
                                                                                        position['maxPurchaseNum'])
          str_position = str_position + str1


  if count > 0:
    str_position = str_position[0:len(str_position)-1]
    str_position = str_position + "\r\n"
    send(conn, str_position.encode())

def query_all(strAcc):
  #print("query all:")
  #logger.info("query all:")
  start_time = time.time()

  query_position(strAcc)
  query_stock_asset(strAcc)
  query_credit_detail(strAcc)
  query_order(strAcc)


  #query_trade(strAcc)
  end_time = time.time()
  #print(f'query耗时{end_time-start_time:.2f}')
  #logger.info(f'query耗时{end_time-start_time:.2f}')

def process_command(line):
    listCmd = line.split(" ")
    cmd = listCmd[0]
    if cmd.find("heart") == -1:
      logger.info(line)
    if cmd == "heart" :
      send(conn, (line+"\r\n").encode(encoding='UTF-8'))   # send data to the client
    elif cmd == "position":
      query_all("")
    elif cmd == "order_stock_async":
      account = account_map[listCmd[1]]
      seq = xt_trader.order_stock_async(account, listCmd[2], int(listCmd[3]), int(listCmd[4]),  int(listCmd[5]),
                                        float(listCmd[6]), listCmd[7], listCmd[8])
      if seq < 0:
        logger.info("order_stock_async = " + str(seq))
      #print("order_stock_async = " + str(seq))
    elif cmd == "cancel_async_sys":
      account = account_map[listCmd[1]]
      xt_trader.cancel_order_stock_sysid_async(account, int(listCmd[2]), int(listCmd[3]))
    elif cmd == "cancel_async":
      account = account_map[listCmd[1]]
      xt_trader.cancel_order_stock_async(account, int(listCmd[2]))
    elif cmd == "ipo":
      query_ipo("")
      query_npl("")

def server():
    # get the hostname
    host = "127.0.0.1" #socket.gethostname()
    #port = 9876  # initiate port no above 1024

    server_socket = socket.socket()  # get instance
    # look closely. The bind() function takes tuple as argument
    server_socket.bind((host, port))  # bind host address and port together

    # configure how many client the server can listen simultaneously
    print("server running ")
    logger.info("server running: " + str(port))
    server_socket.listen(1)
    global conn
    conn, address = server_socket.accept()  # accept new connection
    print("Connection from: " + str(address))
    logger.info("Connection from: " + str(address))
    buffer = ""
    while True:
        # receive data stream. it won't accept data packet greater than 1024 bytes
        try:
          data = conn.recv(1024).decode()
          if not data:
              #break
              #server_socket.listen(1)
              print("None data, relisten")
              logger.info("None data, relisten")
              conn, address = server_socket.accept()  # accept new connection

          #print("from  user: " + str(data))
          buffer += data
          while "\r\n" in buffer:
                line, buffer = buffer.split("\r\n", 1)
                process_command(line)
        except socket.error as e:
          print( e )
          logger.error(e)
          #server_socket.listen(1)
          conn, address = server_socket.accept()  # accept new connection


    conn.close()  # close the connection
    print("server exit ")
    logger.info("server exit ")

if __name__ == "__main__":
    # path为mini qmt客户端安装目录下userdata_mini路径

    # 创建资金账号为**********的证券账号对象
    #acc = StockAccount('*********', 'CREDIT')
    # StockAccount可以用第二个参数指定账号类型，如沪港通传'HUGANGTONG'，深港通传'SHENGANGTONG'
    # acc = StockAccount('**********','STOCK')
    # 创建交易回调类对象，并声明接收回调
    callback = MyXtQuantTraderCallback()
    xt_trader.register_callback(callback)
    # 启动交易线程
    xt_trader.start()
    # 建立交易连接，返回0表示连接成功
    connect_result = xt_trader.connect()
    if connect_result != 0:
        import sys
        logger.info('链接失败，程序即将退出 %d'%connect_result)
        sys.exit('链接失败，程序即将退出 %d'%connect_result)

    accs = xt_trader.query_account_infos()
    for a in accs:
        print("{0} {1}".format(a.account_type, a.account_id))
        logger.info("{0} {1}".format(a.account_type, a.account_id))
        account = None
        if a.account_type == 3 or a.broker_type == 3:
          account_map[a.account_id] = StockAccount(a.account_id,   'CREDIT')
        elif a.account_type == 2 or a.broker_type == 2:
          account_map[a.account_id] = StockAccount(a.account_id, 'STOCK')
        else:
          account_map[a.account_id] = StockAccount(a.account_id, 'STOCK')

    # 对交易回调进行订阅，订阅后可以收到交易主推，返回0表示订阅成功
    for item in account_map.items():
      key = item[0]
      acc = item[1]
      subscribe_result = xt_trader.subscribe(acc)
      if subscribe_result != 0:
        print('账号订阅失败 %d'%subscribe_result)
        logger.info('账号订阅失败 %d'%subscribe_result)
      print(subscribe_result)

    query_all("")

    # 查询当日所有的持仓


    # 阻塞线程，接收交易推送
    server()
    #xt_trader.run_forever()
