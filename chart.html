<!DOCTYPE html>
<html style="height: 100%">
   <head>
       <meta charset="utf-8">
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
       <style>
           /* Existing styles */
           .dropdown-content {
               display: none;
               position: absolute;
               background-color: #f9f9f9;
               min-width: 160px;
               box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
               z-index: 1000;
           }
           .dropdown-content a {
               color: black;
               padding: 12px 16px;
               text-decoration: none;
               display: block;
           }
           .dropdown-content a:hover {background-color: #f1f1f1}

           /* New styles for progress bar */
           #progressBarContainer {
               display: none;
               position: fixed;
               top: 0;
               left: 0;
               width: 100%;
               height: 5px;
               background-color: #f3f3f3;
               z-index: 1001;
           }
           #progressBar {
               width: 0%;
               height: 100%;
               background-color: #4CAF50;
               transition: width 0.3s;
           }
       </style>
   </head>
   <body style="height: 100%; margin: 0">
       <div id="container" style="height: 100%"></div>
	   <div id="dropdownMenu" class="dropdown-content"></div>
       <div id="progressBarContainer">
           <div id="progressBar"></div>
       </div>
       <script type="text/javascript" src="echarts.common.min.js"></script>

       <script type="text/javascript">
	


// Function to show progress bar
function showProgressBar() {
   var progressBarContainer = document.getElementById('progressBarContainer');
   progressBarContainer.style.display = 'block';
   setProgress(0); // Start at 0%
}

// Function to set progress
function setProgress(percent) {
   var progressBar = document.getElementById('progressBar');
   progressBar.style.width = percent + '%';
   if (percent >= 100) {
	   setTimeout(function() {
		   document.getElementById('progressBarContainer').style.display = 'none';
	   }, 100); // Hide after 300ms when 100% is reached
   }
}

function hiedProgressBar() {
   var progressBarContainer = document.getElementById('progressBarContainer');
   progressBarContainer.style.display = 'none';
   setProgress(0); // Start at 0%
}
	
function strToDate(str)
{
  var arys= new Array();
  arys=str.split('-');
  var newDate=new Date(arys[0],arys[1],arys[2]); 
  return newDate;
  
}

function MaxIndex(arr)
{
	var max = -50000;
	var index = -1;
	for(var i=0;i<arr.length;i++){ 
	  if(max<arr[i]){
		max=arr[i];
		index = i;
	  }
	}
	return index;
}

function MinIndex(arr)
{
	var max = 500000000;
	var index = -1;
	for(var i=0;i<arr.length;i++){ 
	  if(max>arr[i]){
		max=arr[i];
		index = i;
	  }
	}
	return index;
}

var dom = document.getElementById("container");
var myChart = echarts.init(dom);

function RefreshChart()
{

}

var app = {};
option = null;

var date = [];


var data = [];
var data2 = [];
var data3 = [];
var data4 = [];
var data5 = [];
var data6 = [];

var count = external.GetCapitalCount();
var indexBase = 1;
var indexName = external.GetIndxeTitle();
var switchName = indexName.indexOf("上益") != -1  ? "对比深成" : indexName.indexOf("上证") != -1 ? "显示沪深300的对比" : "显示上证的对比";
var charPeriod = external.GetPeriod();
var charName = '资金曲线-'
var switchName2 = "全部";
var menuVisible = false;
	
if (indexName == "300")
{
	switchName = "对比        上证";
	
}
else if (indexName == "上证")
{
	switchName = "对比        上证收益";
	
}
else if (indexName == "上益")
{
	switchName = "对比        深成";
	
}
else if (indexName == "深成")
{
	switchName = "对比        创业";
		
}
else if (indexName == "创业")
{
	switchName = "对比        50";
		
}
else if (indexName == "50")
{
	switchName = "对比        500";
	
}
else 
{
	switchName = "对比        300";
	
}


if (charPeriod == 0)
{

	switchName2 = "最近        一个月";
}
else if (charPeriod == 1)
{
	switchName2 = "最近        两个月";
	charName+="-近一月";
}
else if (charPeriod == 2)
{
	switchName2 = "最近        三个月";
	charName+="-近两月";
}
else if (charPeriod == 3)
{
	switchName2 = "最近        半年";
	charName+="-近三月";
}
else if (charPeriod == 4)
{
	switchName2 = "最近        一年";
	charName+="-近半年";
}
else if (charPeriod == 5)
{
	switchName2 = "今年";
	charName+="-近一年";
}
else
	charName+="-今年";
	
for (var i = 0; i < count; i++) {
    //var now = new Date(external.GetCapitalDate(i));
	//var now = strToDate(external.GetCapitalDateS(i));
	var now = external.GetCapitalDateS(i);
    date.push(now);
    data.push(external.GetCapital(i));
	data4.push(external.GetAvaliable(i));
	
	if (i == 0){
		
		data2.push(0);
		data3.push(0);
		data5.push(0);
		data6.push(0);
		indexBase = external.GetCapitalIndex(i) ;
	}
	else{
		var change = ((data[i] -  data[0]) / data[0])*100;
		data2.push(change);
		
		if (indexBase == 0)
			data3.push(external.GetCapitalIndex(i));
		else {
			change = ((external.GetCapitalIndex(i) -  indexBase) / indexBase)*100;
			data3.push(change);
		}
		var day_return = data4[i] - data4[i-1];
		if (day_return < 0)
			day_return = 0;
		data5.push(day_return);
		data6.push(data6[i-1] + day_return);
	}
	
	if (i == count-1)
	{
		var compare = data2[i] - data3[i];
		
		var s = compare.toFixed(2) + '%' ;
		if(compare >= 0)
			charName+=" 跑赢" + indexName + s;
		else
			charName+=" 跑输" + indexName + s;	
		s = data2[i].toFixed(2) + '%' ;
		charName = charName.replace("资金曲线-", "资金曲线" + s + "");
	}
}


var data2minIndex = MinIndex(data2);
var data2maxIndex = MaxIndex(data2);

var data3minIndex = MinIndex(data3);
var data3maxIndex = MaxIndex(data3);

var data2min = data2[data2minIndex];
var data2max = data2[data2maxIndex];

var data3min = data3[data3minIndex];
var data3max = data3[data3maxIndex];

var datamin = Math.min(data2min, data3min);
var datamax = Math.max(data2max, data3max);
var yaxismin = data[0] * (1 + datamin/100);
var yaxismax = data[0] * (1 + datamax/100);


var data4minIndex = MinIndex(data6);
var data4maxIndex = MaxIndex(data6);


var data4min = data6[data4minIndex];
var data4max = data6[data4maxIndex];



option = {
    tooltip: {
        trigger: 'axis',
		    formatter:function(params)  
        {  
           var relVal = params[0].name;  
           for (var i = 0, l = params.length; i < l; i++) {  
				if (i == 0)
					relVal += '<br/>' + params[i].seriesName + ' : ' + params[i].value.toFixed(0);  
				else
					relVal += '<br/>' + params[i].seriesName + ' : ' + params[i].value.toFixed(2)+"%";  
            }  
           return relVal;  
        },
        confine: true,  // 限制在图表区域内
		position: function(point, params, dom, rect, size) 
		{
			var posX = point[0];
			var posY = point[1];
			var viewWidth = size.viewSize[0];
			var viewHeight = size.viewSize[1];
			var boxWidth = size.contentSize[0];
			var boxHeight = size.contentSize[1];

			// 获取当前点的索引
			var dataIndex = params[0].dataIndex;
			var totalDataCount = params[0].data.length;
	
			// 如果是最后两个点，将tooltip显示在左侧
			if (dataIndex >= totalDataCount - 2) {
				
				posX = posX - boxWidth;
			} else {
				// 调整tooltip位置，确保其不会超出右边界
				if (posX + boxWidth > viewWidth) {
					posX = posX - boxWidth;
				}
			}

			// 调整tooltip位置，确保其不会超出上/下边界
			if (posY + boxHeight > viewHeight) {
				posY = viewHeight - boxHeight;
			} else if (posY < 0) {
				posY = 0;
			}

			return [posX, posY];
        }
    },
    title: {
        left: 'center',
        text: charName
    },

	legend: {
        data: ['资金', '资金%','可用',  indexName],
        right: '4%',
		 show: true 

    },
	grid: {
		left: '70',
		right: '70'
	},
	toolbox: {
		x:'left',
		left:"4%",
        feature: {

			    myTool1: {
                show: true,
                title: '导出数据',
                icon: 'path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891',
                onclick: function (){
                    external.Command(0);
                }
				},
				myTool12: {
                show: true,
                title: switchName,
                icon: 'path://M433.066667 402.773333a20.48 20.48 0 0 1 29.866666 0l113.066667 113.066667 296.106667-296.106667a21.333333 21.333333 0 0 1 30.293333 0l29.866667 29.866667a21.333333 21.333333 0 0 1 0 30.293333l-341.333334 341.333334a20.48 20.48 0 0 1-29.866666 0l-113.066667-113.066667-296.106667 296.106667a21.333333 21.333333 0 0 1-30.293333 0l-29.866667-29.866667a21.333333 21.333333 0 0 1 0-30.293333z',
                onclick: function (event){
					//external.Command(1);
                       //     var dropdownMenu = document.getElementById('dropdownMenu');
						//	var left = event.clientX;
                        //    var top = event.clientY;
			
                          //  dropdownMenu.style.left = 50 + 'px';
                         //   dropdownMenu.style.top = 30  + 'px';
                         //   dropdownMenu.style.display = 'block';
						  var zr = myChart.getZr();
                            zr.on('click', function (params) {
                                var event = params.event;
                                var dropdownMenu = document.getElementById('dropdownMenu');
                                var chartDom = myChart.getDom();
                                var rect = chartDom.getBoundingClientRect();
                                var left = event.clientX - rect.left;
                                var top = event.clientY - rect.top;

                                dropdownMenu.style.left = left + 'px';
                                dropdownMenu.style.top = top + 'px';
                                dropdownMenu.style.display = 'block';
								event.stopPropagation(); // 阻止事件冒泡
                                zr.off('click'); // 移除事件监听器，防止多次添加
                            });
														
							
                }
				},
				myTool13: {
                show: true,
                title: switchName2,
                icon: 'path://M744.838095 279.161905c-128-128-336.457143-128-465.67619 0-128 128-128 336.457143 0 465.67619 128 128 336.457143 128 465.67619 0 128-128 128-336.457143 0-465.67619z m-30.47619 304.761905H463.238095V344.990476h60.952381v177.980953h190.171429v60.952381z',
                onclick: function (){
					external.Command(2);
                }
				}
        }
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: date,
		axisTick: {
            alignWithLabel: true // 将刻度线与标签对齐
        },
	    axisLabel: {
            interval: 'auto',
			align: 'center', // 确保标签居中对齐
            showMaxLabel: true // 确保最后一个标签（最后一天的日期）始终显示
        }
    },
    yAxis: [{
        type: 'value',
		min: yaxismin,
		max: yaxismax,
		axisLabel: {                   
                formatter: function (value, index) {           
                 return value.toFixed(0);      
                 }    
		 },
	    axisLine: {
          show: true,
          symbol: ['none', 'none'],
		}
		
    },
	{
	   type: 'value',
		min: 'dataMin',
		max: 'dataMax',
		axisLabel: {                   
                formatter: function (value, index) {           
                 return value.toFixed(1) + '%';      
                 }    
		 },
	    axisLine: {
          show: true,
          symbol: ['none', 'none'],
		}
		
    }],
  visualMap: {
              show: false,
              //lt（小于），gt（大于），lte（小于等于），gte（大于等于）
              pieces: [{
                  lte: 0,
				  gte: data2min,
                  color: 'green',
              }, {
                  gt: 0,
				  lte: data2max,
                  color: 'red',
              },
			  
              ],

			  seriesIndex: 1,
          },
    series: [
        {
            name:'资金',
            type:'line',
            smooth:true,
            showSymbol: count < 40, 
            sampling: 'average',
			 yAxisIndex: 0,
            itemStyle: {
                normal: {
                    color: 'rgb(255, 70, 131)'
                }
            },

            data: data
        },
		
		 {
            name:'资金%',
            type:'line',
            smooth:true,
            showSymbol: count < 40, 
            sampling: 'average',
			yAxisIndex: 1,
            itemStyle: {
                normal: {
                    color: 'rgb(255, 70, 131)'
                }
            },
            areaStyle: {
               
            },
            data: data2
        },
		{
            name:indexName,
            type:'line',
            smooth:true,
            showSymbol: count < 40, 
            sampling: 'average',
			yAxisIndex: 1,
            itemStyle: {
                normal: {
                    color: 'rgb(131, 70, 255)'
                }
            },

            data: data3
        }
    ]
};

function OnSize(){
myChart.resize();
}


// Function to create the dropdown menu dynamically
function createDropdownMenu() {
	var dropdownMenu = document.getElementById('dropdownMenu');
	dropdownMenu.innerHTML = ''; // Clear existing items
	menuItems.forEach(function(item) {
		var a = document.createElement('a');
		a.href = '#';
		a.id = item.id;
		a.textContent = item.text;
		dropdownMenu.appendChild(a);
	});
}
// Function to handle menu item clicks
function handleMenuItemClick(event) {
	var id = event.target.id;
	if (id) {
		document.getElementById('dropdownMenu').style.display = 'none';
		external.SetValue('index', id);
	}
	var dropdownMenu = document.getElementById('dropdownMenu');
	dropdownMenu.style.display = 'none';
}
		
if (option && typeof option === "object") {
    myChart.setOption(option, true);
        var menuItems = [
            { id: 'sh000001', text: '上证指数' },
            { id: 'sh000300', text: '沪深300' },
            { id: 'sz399001', text: '深证成指' },
            { id: 'sz399006', text: '创业板指' }, // Fixed duplicate ID
            { id: 'sh000016', text: '上证50' },
            { id: 'sh000905', text: '中证500' },
            { id: 'sh000852', text: '中证1000' },
            { id: 'sh000888', text: '上证收益' },
            { id: 'us.IXIC', text: '纳斯达克' },
            { id: 'us.INX', text: '标普500' },

        ];

		var count = external.GetPositionCount();
		for (var i = 0; i < count; i++) {
			var item = { id: external.GetPositionCode(i), text: external.GetPositionName(i) };
			menuItems.push(item);
		}
        createDropdownMenu();


        // Bind click event to dropdown menu container
        document.getElementById('dropdownMenu').addEventListener('click', handleMenuItemClick);

        // 点击其他地方隐藏下拉菜单
        document.addEventListener('click', function(event) {
            var dropdownMenu = document.getElementById('dropdownMenu');
            if (!dropdownMenu.contains(event.target)) {
                dropdownMenu.style.display = 'none';
            }
        });

}
       </script>
   </body>
</html>